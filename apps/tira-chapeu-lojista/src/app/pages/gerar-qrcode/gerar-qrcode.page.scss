.container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 400px;
}

.qrcode-area {
  padding: 24px;
  margin-bottom: 24px;
  text-align: center;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  h2 {
    margin: 0 0 8px 0;
    color: var(--ion-color-dark);
    font-weight: 600;
  }

  p {
    margin: 0 0 24px 0;
    color: var(--ion-color-medium);
  }

  small {
    display: block;
    margin: 8px 0 0 0;
    color: var(--ion-color-medium-shade);
    font-style: italic;
  }
}

.qrcode-image {
  padding: 24px;
  margin: 24px 0;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    max-width: 100%;
    max-height: 200px;
    width: auto;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  // Fallback para quando não há imagem
  ion-text p {
    margin: 0;
    color: var(--ion-color-medium);
    font-family: monospace;
    font-size: 12px;
    word-break: break-all;
    line-height: 1.4;
  }
}

.placeholder-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.placeholder-area {
  text-align: center;
  padding: 40px 20px;

  .placeholder-icon {
    font-size: 80px;
    color: var(--ion-color-medium-tint);
    margin-bottom: 24px;
  }

  h2 {
    margin: 0 0 8px 0;
    color: var(--ion-color-medium);
    font-weight: 500;
  }

  p {
    margin: 0 0 32px 0;
    color: var(--ion-color-medium-tint);
  }
}

.button-container {
  width: 100%;
  max-width: 400px;
  padding: 0 16px;

  ion-button {
    margin-bottom: 12px;
    --border-radius: 12px;
    height: 48px;
    font-weight: 600;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

ion-item {
  --padding-start: 0;
  --inner-padding-end: 0;
}
