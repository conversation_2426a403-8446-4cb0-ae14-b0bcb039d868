import { Component, OnInit } from '@angular/core';
import { ToastController, ModalController } from '@ionic/angular';
import { AuthService, Credencial, NavigationService, Usuario } from '@mobile/shared';
import { CartaoQRCodeService } from '@mobile/shared';
import { ModalSegurancaComponent } from '@mobile/modals';
import { CartaoQrCode, ImagemCartaoQrCodeVO } from '@mobile/shared';
import { loading, LoadingContext } from '@utils/loading.util';

/* eslint-disable @angular-eslint/component-class-suffix */
@Component({
  selector: 'app-gerar-qrcode',
  templateUrl: './gerar-qrcode.page.html',
  styleUrls: ['./gerar-qrcode.page.scss'],
})
export class GerarQrcodePage implements OnInit {

  cartaoQrCode: CartaoQrCode | null = null;
  imagemQrCode: ImagemCartaoQrCodeVO | null = null;
  carregandoQRCode = true;
  usuario!: Usuario;
  credencial?: Credencial;
  inicioRoute = '';

  constructor(
    private cartaoQRCodeService: CartaoQRCodeService,
    private authService: AuthService,
    private modalController: ModalController,
    private toastController: ToastController,
    private navigationService: NavigationService
  ) {
    this.usuario = this.authService.getUser();
    this.credencial = this.usuario.credenciais[0];
  }

  ngOnInit() {
    this.inicioRoute = this.getInicioRoute();
    this.buscarCartaoQrCode();
  }

  /**
   * Busca cartão QR code existente da conta ao inicializar a tela
   */
  async buscarCartaoQrCode() {
    console.log('Buscar cartão QR code');
    let loadingInstance: LoadingContext | null = null;

    try {
      this.carregandoQRCode = true;
      loadingInstance = await loading('Carregando QR Code...');

      const idConta = this.credencial?.contas[0].idConta;
      if (!idConta) {
        throw new Error('ID da conta não encontrado');
      }

      const cartoesQrCode = await this.cartaoQRCodeService.listarCartoesQrCode(idConta).toPromise();

      if (cartoesQrCode && cartoesQrCode.length > 0) {
        // Pega o primeiro cartão QR code ativo (status 2 = VINCULADO)
        const cartaoAtivo = cartoesQrCode.find(cartao => cartao.status === 1);

        if (cartaoAtivo) {
          this.cartaoQrCode = cartaoAtivo;

          // Busca a imagem do QR code
          const imagemResponse = await this.cartaoQRCodeService.buscarImagemCartaoQrCode(cartaoAtivo.id).toPromise();
          this.imagemQrCode = imagemResponse || null;

          console.log('Cartão QR code encontrado:', cartaoAtivo);
        } else {
          this.cartaoQrCode = null;
          this.imagemQrCode = null;
          console.log('Nenhum cartão QR code ativo encontrado');
        }
      } else {
        this.cartaoQrCode = null;
        this.imagemQrCode = null;
        console.log('Nenhum cartão QR code encontrado para a conta');
      }
    } catch (error) {
      console.error('Erro ao buscar cartão QR code:', error);
      this.cartaoQrCode = null;
      this.imagemQrCode = null;

      const toast = await this.toastController.create({
        message: 'Erro ao carregar QR code',
        duration: 3000,
        color: 'danger'
      });
      await toast.present();
    } finally {
      // Sempre dismissar o loading, independente de sucesso ou erro
      loadingInstance?.dismiss?.();
      this.carregandoQRCode = false;
    }
  }

  /**
   * Verifica a segurança antes de gerar novo QR code
   */
  async verificarSeguranca(): Promise<boolean> {
    if (!this.credencial?.idCredencial) {
      const toast = await this.toastController.create({
        message: 'Erro ao obter dados da credencial',
        duration: 3000,
        color: 'danger'
      });
      await toast.present();
      return false;
    }

    const modal = await this.modalController.create({
      component: ModalSegurancaComponent,
      componentProps: {
        metodoSegurancaTransacao: 1, // 1 = Senha do cartão
        idCredencial: this.credencial.idCredencial,
        documento: this.usuario.documento,
        telefoneCelular: `${this.usuario.dddTelefoneCelular}${this.usuario.telefoneCelular}`
      },
      cssClass: 'modal-half-default'
    });

    await modal.present();
    const { data } = await modal.onWillDismiss();

    return data === true;
  }

  /**
   * Gera novo cartão QR code permanente
   */
  async gerarNovoQRCode() {
    // Verificar segurança antes de prosseguir
    const segurancaValidada = await this.verificarSeguranca();

    if (!segurancaValidada) {
      console.log('Geração de QR code cancelada: segurança não validada');
      return;
    }

    let loadingInstance: LoadingContext | null = null;

    try {
      loadingInstance = await loading('Gerando QR code...');

      const idConta = this.credencial?.contas[0].idConta;
      if (!idConta) {
        throw new Error('ID da conta não encontrado');
      }

      const response = await this.cartaoQRCodeService.gerarCartaoQrCode(idConta).toPromise();

      if (response && response.imagemBase64) {
        console.log('Cartão QR code gerado com sucesso:', response);

        // Atualizar a tela com o novo QR code
        this.imagemQrCode = response;

        // Buscar dados completos do cartão criado
        const cartaoCompleto = await this.cartaoQRCodeService.buscarCartaoQrCode(response.id).toPromise();
        this.cartaoQrCode = cartaoCompleto || null;

        const toast = await this.toastController.create({
          message: 'QR code gerado com sucesso!',
          duration: 3000,
          color: 'success'
        });
        await toast.present();
      } else {
        throw new Error('Resposta inválida do servidor');
      }
    } catch (error) {
      console.error('Erro ao gerar QR code:', error);

      const toast = await this.toastController.create({
        message: 'Erro ao gerar QR code. Tente novamente.',
        duration: 3000,
        color: 'danger'
      });
      await toast.present();
    } finally {
      // Sempre dismissar o loading, independente de sucesso ou erro
      loadingInstance?.dismiss?.();
    }
  }

  /**
   * Cancela/bloqueia QR code atual
   */
  async bloquearQRCode() {
    if (!this.cartaoQrCode) {
      return;
    }

    // Verificar segurança antes de prosseguir
    const segurancaValidada = await this.verificarSeguranca();

    if (!segurancaValidada) {
      console.log('Cancelamento de QR code cancelado: segurança não validada');
      return;
    }

    let loadingInstance: LoadingContext | null = null;

    try {
      loadingInstance = await loading('Cancelando QR code...');

      const response = await this.cartaoQRCodeService.cancelarCartaoQrCode(this.cartaoQrCode.id).toPromise();

      console.log('QR code cancelado:', response);

      // Limpar dados na tela
      this.cartaoQrCode = null;
      this.imagemQrCode = null;

      const toast = await this.toastController.create({
        message: 'QR code cancelado com sucesso',
        duration: 3000,
        color: 'success'
      });
      await toast.present();
    } catch (error) {
      console.error('Erro ao cancelar QR code:', error);

      const toast = await this.toastController.create({
        message: 'Erro ao cancelar QR code. Tente novamente.',
        duration: 3000,
        color: 'danger'
      });
      await toast.present();
    } finally {
      // Sempre dismissar o loading, independente de sucesso ou erro
      loadingInstance?.dismiss?.();
    }
  }

  /**
   * Compartilha o QR code
   */
  compartilhar() {
    if (this.imagemQrCode?.imagemBase64) {
      // TODO: Implementar compartilhamento nativo da imagem
      console.log('Compartilhar QR code:', this.imagemQrCode.id);

      // Por enquanto, apenas mostrar uma mensagem
      this.toastController.create({
        message: 'Funcionalidade de compartilhamento será implementada em breve',
        duration: 3000,
        color: 'warning'
      }).then(toast => toast.present());
    }
  }

  getInicioRoute(): string {
    return this.navigationService.getInicioRoute();
  }
}
