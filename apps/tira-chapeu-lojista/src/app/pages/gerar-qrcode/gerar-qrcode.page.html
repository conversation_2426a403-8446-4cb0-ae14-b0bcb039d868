<ion-header class="ion-no-border">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button [routerLink]="inicioRoute" fill="clear" color="primary">
        <ion-icon name="chevron-back" slot="icon-only"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>QR Code</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <div class="container">

    <!-- QR Code existente -->
    <div class="qrcode-container" *ngIf="!carregandoQRCode && cartaoQrCode && imagemQrCode">
      <div class="qrcode-area">

        <!-- Área do QR Code -->
        <div class="qrcode-image">
          <img [src]="'data:image/png;base64,' + imagemQrCode.imagemBase64" alt="QR Code" />
        </div>

        <ion-button expand="block" fill="clear" (click)="compartilhar()">
          Compartilhar
          <ion-icon name="share-outline" slot="end"></ion-icon>
        </ion-button>
      </div>

      <div class="button-container">
        <!-- Quando há QR code ativo, só mostra o botão de bloquear -->
        <ion-button expand="block" fill="outline" (click)="bloquearQRCode()">
          Bloquear QR Code
        </ion-button>
      </div>
    </div>

    <!-- Placeholder quando não há QR Code -->
    <div class="placeholder-container" *ngIf="!carregandoQRCode && !cartaoQrCode">
      <div class="placeholder-area">
        <ion-icon name="qr-code-outline" class="placeholder-icon"></ion-icon>
        <ion-text>
          <h2>QR Code não encontrado</h2>
          <p>Gere um QR Code para receber pagamentos</p>
        </ion-text>
      </div>

      <div class="button-container">
        <ion-button expand="block" (click)="gerarNovoQRCode()">
          Gerar QR Code
        </ion-button>
      </div>
    </div>

  </div>
</ion-content>
