package br.com.sinergico.controller.cadastral;

import br.com.entity.cadastral.CartaoQrCode;
import br.com.json.bean.cadastral.CountResponseLong;
import br.com.json.bean.cadastral.CriaVinculaEditaOuCancelaCartaoQrCodeVO;
import br.com.json.bean.cadastral.DadosParaCobrarComQrCodeVO;
import br.com.json.bean.cadastral.ImagemCartaoQrCodeVO;
import br.com.json.bean.prontopaguei.response.RecuperarTransacoesClienteResponse;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.cadastral.CartaoQrCodeService;
import br.com.sinergico.service.suporte.TravaServicosService;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.vo.CartaoQrCodeDetalhesVO;
import br.com.sinergico.vo.CartaoQrCodeFiltroVO;
import br.com.sinergico.vo.CartaoQrCodeVO;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/cartao-qr-code")
public class CartaoQrCodeController extends UtilController {

  @Autowired private EntityManager em;

  @Autowired private HttpServletRequest httpServletRequest;

  @Autowired private CartaoQrCodeService cartaoQrCodeService;

  @Autowired private TravaServicosService travaServicosService;

  @ApiOperation(value = "Busca um Cartão QR Code específico.", response = CartaoQrCode.class)
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/{id}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_CARTAO_QR_CODE", "ROLE_BUSCAR_DETALHES_CARTAO_QR_CODE_ISSUER"})
  public ResponseEntity<CartaoQrCode> buscarCartaoQrCode(@PathVariable Long id) {

    SecurityUser user = null;
    SecurityUserPortador userPortador = null;
    Integer idInstituicao = null;
    try {
      user = getAuthenticatedUser(httpServletRequest, em);
      idInstituicao = user.getIdInstituicao();
    } catch (Exception e) {
      userPortador = getAuthenticatedPortador(httpServletRequest, em);
      idInstituicao = userPortador.getIdInstituicao();
    }

    travaServicosService.travaServicos(idInstituicao, Servicos.CARTAO_QR_CODE);

    CartaoQrCode cartaoQrCode =
        callFunctionUserOrFunctionPortador(
            httpServletRequest,
            id,
            cartaoQrCodeService::detalhaUmQrCodeUsuario,
            cartaoQrCodeService::detalhaUmQrCodePortador,
            em);

    return new ResponseEntity<>(cartaoQrCode, HttpStatus.OK);
  }

  @ApiOperation(value = "Retorna Imagem Base64 de um Cartão QR Code.", response = String.class)
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/imagem/{id}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_IMAGEM_CARTAO_QR_CODE", "ROLE_BUSCAR_IMAGEM_CARTAO_QR_CODE_ISSUER"})
  public ResponseEntity<ImagemCartaoQrCodeVO> buscaImagemCartaoQrCode(@PathVariable Long id) {

    SecurityUser user = null;
    SecurityUserPortador userPortador = null;
    Integer idInstituicao = null;
    try {
      user = getAuthenticatedUser(httpServletRequest, em);
      idInstituicao = user.getIdInstituicao();
    } catch (Exception e) {
      userPortador = getAuthenticatedPortador(httpServletRequest, em);
      idInstituicao = userPortador.getIdInstituicao();
    }

    travaServicosService.travaServicos(idInstituicao, Servicos.CARTAO_QR_CODE);

    ImagemCartaoQrCodeVO imagemCartaoQrCodeVO =
        callFunctionUserOrFunctionPortador(
            httpServletRequest,
            id,
            cartaoQrCodeService::apresentaUmQrCodeUsuario,
            cartaoQrCodeService::apresentaUmQrCodePortador,
            em);

    return new ResponseEntity<>(imagemCartaoQrCodeVO, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Lista Cartões QR Code por ID Conta, Status, Chave pre-vinculação ou apelido.",
      response = List.class)
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/listar-qr-codes",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LISTAR_CARTAO_QR_CODE", "ROLE_LISTAR_CARTAO_QR_CODE_ISSUER"})
  public ResponseEntity<List<CartaoQrCode>> listarCartoesQrCode(
      @RequestBody CriaVinculaEditaOuCancelaCartaoQrCodeVO request) {

    SecurityUser user = null;
    SecurityUserPortador userPortador = null;
    Integer idInstituicao = null;
    try {
      user = getAuthenticatedUser(httpServletRequest, em);
      idInstituicao = user.getIdInstituicao();
    } catch (Exception e) {
      userPortador = getAuthenticatedPortador(httpServletRequest, em);
      idInstituicao = userPortador.getIdInstituicao();
    }

    travaServicosService.travaServicos(idInstituicao, Servicos.CARTAO_QR_CODE);

    List<CartaoQrCode> cartaoQrCodeList =
        callFunctionUserOrFunctionPortador(
            httpServletRequest,
            request,
            cartaoQrCodeService::listaQrCodesUsuario,
            cartaoQrCodeService::listaQrCodesPortador,
            em);

    return new ResponseEntity<>(cartaoQrCodeList, HttpStatus.OK);
  }

  @ApiOperation(value = "Contar Cartões QR Code utilizando filtros completos")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      method = RequestMethod.POST,
      path = "/busca-filtrada/cartoes-qr-code/count",
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LISTAR_CARTAO_QR_CODE_ISSUER"})
  public ResponseEntity<CountResponseLong> countCartoesComFiltros(
      @RequestBody CartaoQrCodeFiltroVO filtroVO) {

    SecurityUser user = getAuthenticatedUser(httpServletRequest, em);
    Long count = cartaoQrCodeService.contaElementos(filtroVO, user);

    return new ResponseEntity<>(new CountResponseLong(count), HttpStatus.OK);
  }

  @ApiOperation(value = "Listar Cartões QR Code utilizando filtros completos")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      method = RequestMethod.POST,
      path = "/busca-filtrada/cartoes-qr-code",
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LISTAR_CARTAO_QR_CODE_ISSUER"})
  public ResponseEntity<?> buscaCartoesComFiltros(@RequestBody CartaoQrCodeFiltroVO filtroVO) {

    SecurityUser user = getAuthenticatedUser(httpServletRequest, em);
    List<CartaoQrCodeVO> retorno = cartaoQrCodeService.listaComFiltros(filtroVO, user);

    return new ResponseEntity<>(retorno, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Gera um novo Cartão QR Code e retorna a imagem Base64 dele.",
      response = String.class)
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/gerar-qr-code",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_GERAR_CARTAO_QR_CODE", "ROLE_GERAR_CARTAO_QR_CODE_ISSUER"})
  public ResponseEntity<ImagemCartaoQrCodeVO> gerarCartaoQrCode(
      @RequestBody CriaVinculaEditaOuCancelaCartaoQrCodeVO request) {

    SecurityUser user = null;
    SecurityUserPortador userPortador = null;
    Integer idInstituicao = null;
    try {
      user = getAuthenticatedUser(httpServletRequest, em);
      idInstituicao = user.getIdInstituicao();
    } catch (Exception e) {
      userPortador = getAuthenticatedPortador(httpServletRequest, em);
      idInstituicao = userPortador.getIdInstituicao();
    }

    travaServicosService.travaServicos(idInstituicao, Servicos.CARTAO_QR_CODE);

    ImagemCartaoQrCodeVO imagemCartaoQrCodeVO =
        callFunctionUserOrFunctionPortador(
            httpServletRequest,
            request,
            cartaoQrCodeService::gerarQrCodeUsuario,
            cartaoQrCodeService::gerarQrCodePortador,
            em);

    return new ResponseEntity<>(imagemCartaoQrCodeVO, HttpStatus.OK);
  }

  @ApiOperation(value = "Vincula um Cartão QR Code a uma conta.", response = CartaoQrCode.class)
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/vincular-qr-code",
      method = RequestMethod.PUT,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_VINCULAR_CARTAO_QR_CODE", "ROLE_VINCULAR_CARTAO_QR_CODE_ISSUER"})
  public ResponseEntity<CartaoQrCode> vincularCartaoQrCode(
      @RequestBody CriaVinculaEditaOuCancelaCartaoQrCodeVO request) {

    SecurityUser user = null;
    SecurityUserPortador userPortador = null;
    Integer idInstituicao = null;
    try {
      user = getAuthenticatedUser(httpServletRequest, em);
      idInstituicao = user.getIdInstituicao();
    } catch (Exception e) {
      userPortador = getAuthenticatedPortador(httpServletRequest, em);
      idInstituicao = userPortador.getIdInstituicao();
    }

    travaServicosService.travaServicos(idInstituicao, Servicos.CARTAO_QR_CODE);

    CartaoQrCode cartaoQrCode =
        callFunctionUserOrFunctionPortador(
            httpServletRequest,
            request,
            cartaoQrCodeService::vincularQrCodeUsuario,
            cartaoQrCodeService::vincularQrCodePortador,
            em);

    return new ResponseEntity<>(cartaoQrCode, HttpStatus.OK);
  }

  @ApiOperation(value = "Cancela um Cartão QR Code.", response = CartaoQrCode.class)
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/{id}",
      method = RequestMethod.DELETE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CANCELAR_CARTAO_QR_CODE", "ROLE_CANCELAR_CARTAO_QR_CODE_ISSUER"})
  public ResponseEntity<CartaoQrCode> cancelarCartaoQrCode(@PathVariable Long id) {

    SecurityUser user = null;
    SecurityUserPortador userPortador = null;
    Integer idInstituicao = null;
    try {
      user = getAuthenticatedUser(httpServletRequest, em);
      idInstituicao = user.getIdInstituicao();
    } catch (Exception e) {
      userPortador = getAuthenticatedPortador(httpServletRequest, em);
      idInstituicao = userPortador.getIdInstituicao();
    }

    travaServicosService.travaServicos(idInstituicao, Servicos.CARTAO_QR_CODE);

    CartaoQrCode cartaoQrCode =
        callFunctionUserOrFunctionPortador(
            httpServletRequest,
            id,
            cartaoQrCodeService::cancelarQrCodeUsuario,
            cartaoQrCodeService::cancelarQrCodePortador,
            em);

    return new ResponseEntity<>(cartaoQrCode, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Atualiza apelido de um Cartão QR Code.",
      response = RecuperarTransacoesClienteResponse.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/atualizar-apelido",
      method = RequestMethod.PUT,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_EDITAR_CARTAO_QR_CODE"})
  public ResponseEntity<CartaoQrCode> atualizarApelidoCartaoQrCode(
      @RequestBody CriaVinculaEditaOuCancelaCartaoQrCodeVO request) {

    SecurityUserPortador userPortador = getAuthenticatedPortador(httpServletRequest, em);

    travaServicosService.travaServicos(userPortador.getIdInstituicao(), Servicos.CARTAO_QR_CODE);

    CartaoQrCode cartaoQrCode =
        cartaoQrCodeService.atualizarApelidoQrCodePortador(request, userPortador);

    return new ResponseEntity<>(cartaoQrCode, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Resgata dados da Conta associada a um QR Code lido.",
      response = RecuperarTransacoesClienteResponse.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/busca-id-conta/{qrCodeLido}",
      method = RequestMethod.GET,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LER_CARTAO_QR_CODE"})
  public ResponseEntity<DadosParaCobrarComQrCodeVO> resgataIdContaPorQrCodeLido(
      @PathVariable String qrCodeLido) {

    SecurityUserPortador userPortador = getAuthenticatedPortador(httpServletRequest, em);

    travaServicosService.travaServicos(userPortador.getIdInstituicao(), Servicos.CARTAO_QR_CODE);

    DadosParaCobrarComQrCodeVO dados =
        cartaoQrCodeService.retornaDadosParaCobrarComQrode(qrCodeLido);

    return new ResponseEntity<>(dados, HttpStatus.OK);
  }

  @ApiOperation(value = "Busca todos os dados relacionados a associação do cartão QR Code.")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar-dados-extras/{idCartaoQrCode}",
      method = RequestMethod.GET,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_DETALHES_CARTAO_QR_CODE_ISSUER"})
  public ResponseEntity<Object> buscarDadosExtrasCartaoQrCode(
      @PathVariable Integer idCartaoQrCode) {
    SecurityUser user = getAuthenticatedUser(httpServletRequest, em);

    travaServicosService.travaServicos(user.getIdInstituicao(), Servicos.CARTAO_QR_CODE);

    CartaoQrCodeDetalhesVO cartaoQrCodeDetalhesVO =
        cartaoQrCodeService.buscarDetalhesCartaoQrCode(
            idCartaoQrCode, Constantes.ID_PROCESSADORA_ITS_PAY);
    return new ResponseEntity<>(cartaoQrCodeDetalhesVO, HttpStatus.OK);
  }
}
