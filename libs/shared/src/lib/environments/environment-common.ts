import * as _ from 'lodash';
import { AppEnvironment } from '../interfaces';

export const environment: AppEnvironment = {
  production: false,
  appId: 'br.com.valloo.id',
  app: 'valloo-v2',
  appName: 'Valloo',
  env: 'commom',
  default: 'valloo',
  idInstituicao: 2001,
  idProcessadora: 10,
  idProgramaFidelidade: 2,
  idProdutoInstituicao: 200101,
  isbp: 0,
  validarCadastroComCaf: true,
  tokenCaf: 'eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiI2NTU2NWZkMjNmMjA3ZjAwMDgwNmMyMmIifQ.L5oTCAAAho3IuThBlNpsD7da238zOh-TaXwPc6IxVmU',
  tokenFaceLiveness: 'eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiI2NTU2NWZkMjNmMjA3ZjAwMDgwNmMyMmIifQ.L5oTCAAAho3IuThBlNpsD7da238zOh-TaXwPc6IxVmU',
  tokenCafBeta: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiI2NTljNzMxYWUyMzY2YzAwMDhiOWIzM2UifQ.QoddeSSVsQrm1DeYyCnhW7Wny928_EtiagHPBchsIuo',
  idGrupoAcesso: 40,
  idAplicativo: 8,
  idGrupoAcessoPj: 42,
  urlMotiva: 'https://motiva.itspay-hom.com.br',
  onesignalAppId: '',
  hosts: {
    valloo: {
      host: '',
      protocol: 'https',
      port: '',
      root: '/api/api',
      endpoints: {
        api: '',
        auth: 'portador/login/auth',
        logout: 'auth/logout',
        dadosUsuario: 'loyalty/dados-principais/:idProgramaFidelidade',
        credencial: 'portador/credencial',
        permissoesPortador: 'gerenciador/aplicativo/permissions-portador/:idConta/:nomeApp',
        rotator: 'loyalty/rotator',
        extrato: 'portador/conta/:idConta/extrato',
        downloadExtrato: 'download/extrato',
        buscarComprovante: 'comprovante-pagamento/consultar',
        portador: 'portador',
        pix: 'pix',
        portadorPix: 'portador/pix',
        buscarSaldo: 'portador/conta/buscar-saldo-conta/:idConta',
        tokenFuncionalidade: 'token-funcionalidade',
        tokenAcesso: 'token-acesso',
        validarCadastro: 'portador/login/validar-cadastro-onboard',
        validarCadastroSemCaf: 'portador/login/validar-cadastro-onboard-sem-caf',
        validarCaf: 'antifraude/validar-ocr/caf',
        portadorLogin: 'portador/login',
        registroFacial: 'antifraude/caf/registrar-validacao-facial/:idInstituicao/:documento/:idObjetivo',
        redefinirSenha: 'portador/login/redefinir-senha',
        antifraude: 'antifraude',
        enviarTokenSenha: 'token-redefinicao-senha/enviar/token-redefinir-senha',
        funcionalidades: 'gerenciador/aplicativo/buscar-servicos-habilitados/:idApp',
        diretrizes: 'diretrizes/:idApp/:tipo',
        verificarVersao: 'versao-app/verifica-versao',
        tranferirMesmoBolso: 'portador/conta/v2/transferencia/mesmo-bolso',
        plastico: 'plastico/abrir/mobile/:idPlastico',
        credencialDetalhe: 'portador/credencial/:idCredencial/detalhes',
        validarEtapasCredencial: 'resgate-pontos/validar-etapas-credencial',
        desbloqueio: 'portador/desbloqueio/solicitar-desbloqueio-cartao',
        salvarSenha: 'resgate-pontos/mobile/salvarCredencial',
        criarCartaoVirtual: 'gerador/credencial',
        campanha: 'campanha',
        gerarTokenMotiva: 'token-funcionalidade/gerar-token',
        buscarTarifa: 'perfiltarifario/:idPerfil/codTransacao/:codTransacao',
        gerarCartaoVirtual: 'gerador/credencial',
        perfilTarifario: 'perfiltarifario/conta/:idConta',
        verificarQrcode: 'elo/qrcode/parse',
        pagarQrcode: 'elo/qrcode/send-transaction',
        endereco: 'endereco',
        cep: 'logradouro/card-holder/:cep',
        faleConosco: 'instituicao/get/instituicao',
        saqueFgts: 'fgts/registro-mensagem',
        parametro: 'parametro',
        prontoPaguei: 'pronto-paguei',
        limites: 'limites',
        pagamento: 'gateway-pagamento-externo',
        voucher: 'pedido-produto-giftty',
        recarga: 'gateway-pagamento-externo/recarga',
        banco: 'banco',
        ted: 'ted/efetuar',
        limite: 'limites/verificar-limite',
        notificacoes: 'aplicativo-mensagem',
        migracao: 'migracao',
        extratoMigracao: 'portador/conta/:idConta/extrato',
        consultarComprovanteMigracao: 'migracao/consultar-comprovante',
        downloadExtratoMigracao: 'migracao/download-extrato',
        cancelarCredenciaisMigradas: 'migracao/alterar-status/credenciais',
        buscarTarifas: 'portador/conta/buscar-tarifas/conta/:idConta',
        buscarEmail: 'portador/login/:idProcessadora/:idInstituicao/buscar-email/:documento',
        enviarBoletoEmail: 'boleto/carga/gerar-cobranca-boleto/portador/enviar-boleto-email',
        gerarLinhaDigitavel: 'boleto/carga/gerar-cobranca-boleto/portador',
        gerarLinhaDigitavelFatura: 'portador/fatura/linha/:idConta/:anoMes',
        baixarFaturaPDF: 'portador/fatura/download/:idConta/:anoMes',
        habilitarReceberNotificacao: 'portador-dispositivo/habilitar-receber-notificacao',
        google: 'google-maps-api',
        dadosConsumo: 'portador/conta/buscar-dados-consumo/:idConta',
        lerQRCodeLojista: 'estabelecimento/lerQRCode',
        pagarQRCodeLojista: 'estabelecimento/pagarQRCode',
        gerarCartaoQrCode: 'cartao-qr-code/gerar-qr-code',
        listarCartoesQrCode: 'cartao-qr-code/listar-qr-codes',
        buscarCartaoQrCode: 'cartao-qr-code',
        buscarImagemCartaoQrCode: 'cartao-qr-code/imagem/:idConta',
      }
    }
  },
  buttonType: 'rounded',
  buttonLayout: 'carousel',
  homeVersion: 'v1',
  homeSubVersion: '1',
  showChat: true,
  showIcon: true,
  showTitleDots: true,
  tituloAjuda: 'Ajuda',
  tipoLoginPj: 'MULTICONTAS_VALLOO_PJ',
  tipoLoginPf: '',
  urlApple: '',
  corFuncionalidades: false,
  telefoneContato: ''
};
export const merge = (...env: AppEnvironment[]): AppEnvironment => _.merge({}, environment, ...env);
